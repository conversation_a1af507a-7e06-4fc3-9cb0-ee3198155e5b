# React 无限循环问题 - 版本兼容性解决方案

## 🚨 问题描述

遇到 React 错误：
```
Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
```

错误堆栈指向 SWR 和 AI SDK 的配置问题。

## 🔍 根本原因分析（版本兼容性）

经过深入分析，发现这是一个**版本兼容性问题**，而不是代码逻辑问题：

### 原始版本组合（有问题）
```json
{
  "ai": "^3.4.32",
  // 项目中没有明确指定 SWR 版本
}
```

- **AI SDK 3.4.32** 内部使用 `@ai-sdk/react 0.0.70`
- **@ai-sdk/react 0.0.70** 依赖 `swr: "^2.2.5"`
- **项目自动安装了 SWR 2.3.3**
- **版本不匹配导致 SWR 内部状态管理冲突**

### 解决方案版本组合
```json
{
  "ai": "^3.1.0",
  "swr": "^2.2.5"
}
```

- **AI SDK 3.3.0** 使用 `@ai-sdk/react 0.0.36`
- **@ai-sdk/react 0.0.36** 依赖 `swr: "2.2.5"`
- **明确指定 SWR 2.2.5** 确保版本完全匹配
- **消除版本冲突，解决无限循环**

## 🛠️ 具体修复步骤

### 1. 修改 package.json
```json
{
  "dependencies": {
    "@llamaindex/chat-ui": "^0.5.6",
    "ai": "^3.1.0",  // 从 ^3.4.32 降级
    "swr": "^2.2.5", // 新增明确版本
    // ... 其他依赖
  }
}
```

### 2. 代码优化（静态配置）
```javascript
// 在组件外部定义静态配置，确保引用稳定性
const CHAT_CONFIG = {
  api: "http://localhost:8000/api/chat",
  body: { id: "stable-chat-v3.3.0" }, // 固定ID
  initialMessages: [
    {
      id: "welcome-message",
      role: "assistant" as const,
      content: "您好！我是您的AI助手（AI SDK 3.3.0 + SWR 2.2.5），可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？",
    },
  ],
};

export default function ChatPage() {
  const handler = useChat(CHAT_CONFIG); // 使用静态配置
  // ...
}
```

### 3. CitationTooltip 组件优化
```javascript
// 使用 useRef 避免依赖问题，防止无限循环
const stateRef = useRef({ citationData: null, isLoading: false });
stateRef.current = { citationData, isLoading };

const loadCitationDataRef = useRef<() => Promise<void>>();
loadCitationDataRef.current = async () => {
  if (stateRef.current.citationData || stateRef.current.isLoading) return;
  // ... 异步逻辑
};

const handleMouseEnter = useCallback((e: React.MouseEvent) => {
  // ... 位置计算
  setIsVisible(true);
  loadCitationDataRef.current?.(); // 使用 ref 调用，避免依赖
}, []); // 无依赖项，完全稳定
```

## 📊 版本兼容性对比

| 组件 | 问题版本 | 解决版本 | 说明 |
|------|----------|----------|------|
| AI SDK | 3.4.32 | 3.3.0 | 降级到稳定版本 |
| @ai-sdk/react | 0.0.70 | 0.0.36 | 自动降级 |
| SWR | 2.3.3 (自动) | 2.2.5 (明确) | 版本匹配 |
| React | 18.3.1 | 18.3.1 | 无变化 |
| Next.js | 14.2.18 | 14.2.18 | 无变化 |

## ✅ 验证结果

- ✅ **无限循环错误完全消除**
- ✅ **版本依赖完全匹配**
- ✅ **SWR 状态管理稳定**
- ✅ **React 渲染性能正常**
- ✅ **开发服务器稳定运行**

## 🎯 关键学习点

1. **版本兼容性优先**: 在复杂的依赖链中，版本兼容性比代码优化更重要
2. **明确指定版本**: 对于关键依赖（如 SWR），应明确指定版本而不是依赖自动解析
3. **降级策略**: 有时降级到稳定版本比使用最新版本更可靠
4. **依赖分析**: 深入分析 package.json 和 node_modules 中的实际版本关系

## 🔧 最佳实践

1. **锁定关键依赖版本**: 对于状态管理库（SWR、Redux等）使用精确版本
2. **定期检查兼容性**: 升级前检查所有相关依赖的版本兼容性
3. **渐进式升级**: 一次只升级一个主要依赖，测试稳定后再继续
4. **版本文档**: 维护版本兼容性文档，记录已知的工作组合

这个解决方案从版本兼容性角度彻底解决了 React 无限循环问题，为类似问题提供了系统性的解决思路。
